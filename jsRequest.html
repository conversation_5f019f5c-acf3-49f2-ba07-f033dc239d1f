<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <h2>helloo</h2>
    <div id="content"></div>
</body>

<script>
    function getPost(){
         let requast = new XMLHttpRequest();
    requast.open("GET","https://jsonplaceholder.typicode.com/posts");
    requast.responseType = "json";
    requast.send();
    requast.onload = function(){
        if(requast.status >= 200 && requast.status < 300){
          console.log(requast.status);
        let data = requast.response;
       for(post of data){
       
        document.getElementById("content").innerHTML += `<h2>${post.title}</h2>`;

       };///haka jabthom mn backend jsonplaceholder
        }else{
            alert("error");
        ///bach tatba3lak response
    }
///haka wlat type ta7a object (array) lanou js ta3raf ghir 
    ///string

    }
    }
    

    ///post request
    function createnewPost(){
        let requast = new XMLHttpRequest();
        requast.open("POST","https://jsonplaceholder.typicode.com/posts");
        requast.responseType = "json";
        requast.setRequestHeader("Content-type","application/json; charset=UTF-8");
        let bodyParams = JSON.stringify({
            title: "foo",
            body: "bar",
            userId: 1,
          })

        requast.send(bodyParams);
        requast.onload = function(){
            if(requast.status >= 200 && requast.status < 300){
                console.log(requast.status);
            let data = requast.response;
            console.log(data);
            console.log("the status is:"+requast.status);
            alert("post created");
            }else{
                alert("error");
            };

        }

    }
    ///updating a resource
    function updatePost(){
        let requast = new XMLHttpRequest();
        requast.open("PUT","https://jsonplaceholder.typicode.com/posts/1");
        requast.responseType = "json";
        requast.setRequestHeader("Content-type","application/json; charset=UTF-8");
        let bodyParams = JSON.stringify({
            id: 1,
            title: "foo",
            body: "bar",
            userId: 1,
          })

        requast.send(bodyParams);
        requast.onload = function(){
            if(requast.status >= 200 && requast.status < 300){
                console.log(requast.status);
            let data = requast.response;
            console.log(data);
            console.log("the status is:"+requast.status);
            alert("post updated");
            }else{
                alert("error");
            };

        };

    }  
    ///deleting a resource
    function deletePost(){
        let requast = new XMLHttpRequest();
        requast.open("DELETE","https://jsonplaceholder.typicode.com/posts/1");
        requast.responseType = "json";
        requast.setRequestHeader("Content-type","application/json; charset=UTF-8");
        requast.send();
        requast.onload = function(){
            if(requast.status >= 200 && requast.status < 300){
                console.log(requast.status);
            let data = requast.response;
            console.log(data);
            console.log("the status is:"+requast.status);
            alert("post deleted");
            }else{
                alert("error");
            };


        }

    }
/// patch ki thab dir edit lhaja mo3ayna brk mn data par exemple title brk
/// bsh put tastabdal post kml dilou edit lih kml


   
   getPost();

///getpostwithuserfilter
function getPostWithUserFilter(){
    let requast = new XMLHttpRequest();
    requast.open("GET","https://jsonplaceholder.typicode.com/posts?userId=1");
    requast.responseType = "json";
    requast.send();
    requast.onload = function(){
        if(requast.status >= 200 && requast.status < 300){
          console.log(requast.status);
        let data = requast.response;
       for(post of data){
        document.getElementById("content").innerHTML += `<h2>${post.title}</h2>`;
       };///haka jabthom mn backend jsonplaceholder
        }
        else{
            alert("error");
        ///bach tatba3lak response
            console.log(requast.response);
    };///haka wlat type ta7a object (array) lanou js ta3raf ghir 
    ///string
}
    
    }   


</script>
</html>