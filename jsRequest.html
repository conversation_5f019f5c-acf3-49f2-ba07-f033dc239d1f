<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <div id="content"></div>
</body>

<script>
    function getPost()
    {
         let requast = new XMLHttpRequest();
    requast.open("GET","https://jsonplaceholder.typicode.com/posts");
    requast.responseType = "json";
    requast.send();
    requast.onload = function(){
        if(requast.status >= 200 && requast.status < 300){
          console.log(requast.status);
        let data = requast.response;
       for(post of data){
        document.getElementById("content").innerHTML += `<h2>${post.title}</h2>`;
       };///haka jabthom mn backend jsonplaceholder
        }else{
            alert("error");
        ///bach tatba3lak response
    }///haka wlat type ta7a object (array) lanou js ta3raf ghir 
    ///string

    };
    ///post request
    function createnewPost(){
        let requast = new XMLHttpRequest();
        requast.open("POST","https://jsonplaceholder.typicode.com/posts");
        requast.responseType = "json";
        requast.setRequestHeader("Content-type","application/json; charset=UTF-8");
        let bodyParams = JSON.stringify({
            title: "foo",
            body: "bar",
            userId: 1,
          },)
            
        requast.send(bodyParams);
        requast.onload = function(){
            if(requast.status >= 200 && requast.status < 300){
                console.log(requast.status);
            let data = requast.response;
            console.log(data);
            console.log("the status is:"+requast.status);
            alert("post created");
            }else{
                alert("error");
            };
             
        }
    }
    
    createnewPost();
   
    


</script>
</html>